import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { Helmet } from 'react-helmet';
import { motion } from 'framer-motion';
import { Code, Copy, Star, Search, Filter, Clock, FileText, Circle, Zap, XCircle, AlertTriangle, Check, ShieldQuestion } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useToast } from '@/components/ui/use-toast';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogTrigger } from "@/components/ui/dialog";
import { apiClient } from '@/lib/apiClient';
import { getHwid } from '@/lib/hwid';
import StarRating from '@/components/StarRating';

const ExecutorList = () => (
  <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
    <div className="bg-card border rounded-lg p-6">
      <h3 className="font-bold text-lg flex items-center text-green-500 mb-3"><Check className="w-5 h-5 mr-2"/>Supported Executors</h3>
      <ul className="space-y-1 text-muted-foreground text-sm list-disc list-inside">
        <li>Arceus X</li><li>AWP</li><li>Codex</li><li>Delta</li><li>Fluxus</li><li>Hydrogen</li><li>Macsploit</li><li>Sirhurt</li><li>Synapse Z</li><li>Vega X</li><li>Volcano</li><li>Wave</li>
      </ul>
    </div>
    <div className="bg-card border rounded-lg p-6">
      <h3 className="font-bold text-lg flex items-center text-yellow-500 mb-3"><AlertTriangle className="w-5 h-5 mr-2"/>Limited Support</h3>
      <ul className="space-y-1 text-muted-foreground text-sm list-disc list-inside">
        <li>JJSploit - Missing Functions</li><li>Solara - Missing Functions</li><li>Xeno - Missing Functions</li>
      </ul>
    </div>
    <div className="bg-card border rounded-lg p-6">
      <h3 className="font-bold text-lg flex items-center text-orange-500 mb-3"><ShieldQuestion className="w-5 h-5 mr-2"/>Variable Compatibility</h3>
      <ul className="space-y-1 text-muted-foreground text-sm list-disc list-inside">
        <li>Swift - Works for some users, not for others</li><li>Visual - Works for some users, not for others</li>
      </ul>
    </div>
  </div>
);

const Scripts = () => {
  const { toast } = useToast();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [scripts, setScripts] = useState([]);
  const [userRatings, setUserRatings] = useState({});
  const [loading, setLoading] = useState(true);

  const calculateAvgRating = (ratings) => {
      if (!ratings || ratings.length === 0) return { avg: 0, count: 0 };
      const numericRatings = ratings.map(r => r.rating);
      const sum = numericRatings.reduce((a, b) => a + b, 0);
      return { avg: sum / numericRatings.length, count: numericRatings.length };
  };

  const fetchScriptsAndRatings = useCallback(async () => {
      setLoading(true);
      const hwid = getHwid();

      try {
          // Fetch scripts using secure API
          const scriptsResult = await apiClient.getScripts();
          const scriptsData = scriptsResult.data;

          // Fetch user ratings using secure API
          const userRatingsResult = await apiClient.getUserRatings(hwid);
          const userRatingsData = userRatingsResult.data;

          const scriptsWithAvgRating = scriptsData.map(script => {
              const { avg, count } = calculateAvgRating(script.script_ratings);
              return { ...script, avgRating: avg, ratingCount: count };
          });

          const userRatingsMap = userRatingsData ? userRatingsData.reduce((acc, r) => {
              acc[r.script_id] = r.rating;
              return acc;
          }, {}) : {};

          setScripts(scriptsWithAvgRating);
          setUserRatings(userRatingsMap);
      } catch (error) {
          console.error('Error fetching scripts:', error);
          toast({ title: "Error", description: "Could not fetch scripts.", variant: "destructive" });
      } finally {
          setLoading(false);
      }
  }, [toast]);

  useEffect(() => {
    fetchScriptsAndRatings();
  }, [fetchScriptsAndRatings]);

  const statusOrder = { 'Online': 1, 'Discontinued': 2, 'Coming Soon': 3 };

  const sortedAndFilteredScripts = useMemo(() => {
    return scripts
      .filter(script => {
        const matchesSearch = script.title.toLowerCase().includes(searchTerm.toLowerCase()) || script.description.toLowerCase().includes(searchTerm.toLowerCase());
        const matchesCategory = selectedCategory === 'all' || script.category === selectedCategory;
        return matchesSearch && matchesCategory;
      })
      .sort((a, b) => {
        const statusDiff = statusOrder[a.status] - statusOrder[b.status];
        if (statusDiff !== 0) return statusDiff;
        return new Date(b.last_updated) - new Date(a.last_updated);
      });
  }, [scripts, searchTerm, selectedCategory]);

  const handleCopy = (scriptCode, scriptTitle) => {
    navigator.clipboard.writeText(scriptCode).then(() => {
        toast({ title: "Copied to clipboard!", description: `Script for ${scriptTitle} is ready to use.` });
    }).catch(err => {
        toast({ title: "Error", description: "Could not copy script to clipboard.", variant: "destructive" });
    });
  };

  const handleRating = async (scriptId, rating) => {
      const hwid = getHwid();
      const oldRating = userRatings[scriptId];
      setUserRatings(prev => ({ ...prev, [scriptId]: rating }));

      try {
          await apiClient.rateScript(scriptId, rating, hwid);
          toast({ title: "Thanks for rating!", description: "Your feedback helps us improve." });
          fetchScriptsAndRatings();
      } catch (error) {
          setUserRatings(prev => ({ ...prev, [scriptId]: oldRating }));
          toast({ title: "Rating Failed", description: error.message, variant: "destructive" });
      }
  };

  const getStatusPill = (status) => {
    switch (status) {
      case 'Online': return <div className="flex items-center text-xs font-medium text-green-500"><Zap className="w-3 h-3 mr-1" />{status}</div>;
      case 'Discontinued': return <div className="flex items-center text-xs font-medium text-red-500"><XCircle className="w-3 h-3 mr-1" />{status}</div>;
      case 'Coming Soon': return <div className="flex items-center text-xs font-medium text-blue-500"><Circle className="w-3 h-3 mr-1 animate-pulse" />{status}</div>;
      default: return <div className="flex items-center text-xs font-medium text-muted-foreground"><AlertTriangle className="w-3 h-3 mr-1" />{status}</div>;
    }
  };

  const categories = useMemo(() => [
    { value: 'all', label: 'All Scripts' },
    ...[...new Set(scripts.map(s => s.category).filter(Boolean))].map(c => ({ value: c, label: c.charAt(0).toUpperCase() + c.slice(1) }))
  ], [scripts]);

  return (
    <>
      <Helmet><title>Scripts - 6FootScripts</title><meta name="description" content="Discover our collection of high-quality scripts for your favorite games." /></Helmet>
      <div className="min-h-screen py-8 px-4"><div className="max-w-7xl mx-auto">
        <motion.div initial={{ opacity: 0, y: 30 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.8 }} className="text-center mb-12">
            <h1 className="text-4xl md:text-6xl font-bold mb-6">Script <span className="bg-gradient-to-r from-primary to-pink-500 bg-clip-text text-transparent">Library</span></h1>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto">Discover powerful scripts to accelerate your gaming experience.</p>
        </motion.div>
        <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.8, delay: 0.2 }} className="mb-12"><div className="bg-card border rounded-2xl p-6"><div className="flex flex-col md:flex-row gap-4"><div className="flex-1 relative"><Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-5 h-5" /><input type="text" placeholder="Search scripts..." value={searchTerm} onChange={(e) => setSearchTerm(e.target.value)} className="w-full pl-10 pr-4 py-3 bg-background border rounded-lg focus:outline-none focus:ring-2 focus:ring-ring"/></div><div className="relative"><Filter className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-5 h-5" /><select value={selectedCategory} onChange={(e) => setSelectedCategory(e.target.value)} className="w-full md:w-auto pl-10 pr-8 py-3 bg-background border rounded-lg focus:outline-none focus:ring-2 focus:ring-ring appearance-none cursor-pointer">{categories.map(category => (<option key={category.value} value={category.value} className="bg-popover text-popover-foreground">{category.label}</option>))}</select></div></div></div></motion.div>
        {loading ? (<div className="text-center py-16"><Code className="w-16 h-16 text-muted-foreground/40 mx-auto mb-4 animate-pulse" /><h3 className="text-2xl font-bold mb-2">Loading Scripts...</h3></div>) : sortedAndFilteredScripts.length > 0 ? (<div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {sortedAndFilteredScripts.map((script, index) => (<motion.div key={script.id} initial={{ opacity: 0, y: 30 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.8, delay: index * 0.1 }} className="group"><div className="bg-card rounded-2xl overflow-hidden border h-full flex flex-col hover:border-primary transition-all duration-300 transform hover:scale-105 hover:shadow-2xl hover:shadow-primary/10">
                {script.image_url && <div className="relative overflow-hidden"><img className="w-full h-48 object-cover group-hover:scale-110 transition-transform duration-300" alt={script.image_prompt || script.title} src={script.image_url} /><div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div></div>}
                <div className="p-6 flex flex-col flex-grow">
                    <div className="flex items-start justify-between mb-3">
                        <h3 className="text-xl font-bold text-foreground group-hover:text-primary transition-colors pr-2">{script.title}</h3>
                        <div className="flex items-center space-x-1 flex-shrink-0"><Star className="w-4 h-4 text-yellow-400 fill-current" /><span className="text-muted-foreground text-sm">{script.avgRating.toFixed(1)} ({script.ratingCount})</span></div>
                    </div>
                    <p className="text-muted-foreground mb-4 leading-relaxed flex-grow">{script.description}</p>
                    <div className="mb-4"><StarRating rating={userRatings[script.id] || 0} onRate={(rating) => handleRating(script.id, rating)} /></div>
                    <div className="flex items-center justify-between mb-4 text-sm text-muted-foreground">{getStatusPill(script.status)}<div className="flex items-center"><Clock className="w-4 h-4 mr-1.5" /><span>{new Date(script.last_updated).toLocaleDateString()}</span></div></div>
                    <div className="mt-auto pt-4 border-t flex space-x-3">
                        <Dialog><DialogTrigger asChild><Button variant="outline" className="flex-1"><FileText className="w-4 h-4 mr-2" />Update Log</Button></DialogTrigger><DialogContent><DialogHeader><DialogTitle>{script.title} - Update Log</DialogTitle><DialogDescription>Here are the latest changes and updates for this script.</DialogDescription></DialogHeader><div className="max-h-[60vh] overflow-y-auto pr-4">{script.update_log?.map((log, logIndex) => (<div key={logIndex} className="mb-4 pb-4 border-b last:border-b-0"><div className="flex justify-between items-baseline"><p className="font-semibold text-foreground">{log.version}</p><p className="text-sm text-muted-foreground">{log.date}</p></div><ul className="mt-2 list-disc list-inside space-y-1 text-sm text-muted-foreground">{log.changes.map((change, changeIndex) => (<li key={changeIndex}>{change}</li>))}</ul></div>))}</div></DialogContent></Dialog>
                        <Button onClick={() => handleCopy(script.code, script.title)} className="flex-1" disabled={script.status !== 'Online'}><Copy className="w-4 h-4 mr-2" />Copy Script</Button>
                    </div>
                </div>
            </div></motion.div>))}
        </div>) : (<motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }} className="text-center py-16"><Code className="w-16 h-16 text-muted-foreground/40 mx-auto mb-4" /><h3 className="text-2xl font-bold mb-2">No Scripts Found</h3><p className="text-muted-foreground">Try adjusting your search or filter criteria</p></motion.div>)}
        <motion.div initial={{ opacity: 0, y: 30 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.8, delay: 0.4 }} className="mt-16"><div className="text-center mb-8"><h2 className="text-3xl font-bold">Executor Information</h2><p className="text-muted-foreground mt-2">Check your executor's compatibility with our scripts.</p></div><ExecutorList /></motion.div>
      </div></div>
    </>
  );
};

export default Scripts;